/* CSS变量定义 */
:root {
  --btn-width-xs: 50px;
  --btn-width-sm: 65px;
  --btn-width-md: 75px;
  --btn-width-lg: 90px;
  --btn-width-xl: 110px;
}

.container {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
}

/* 卡片样式 */
.card {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #E2E8F0;
}

/* 显示屏样式 */
.display {
  margin-bottom: 20px;
  min-height: 120px;
}

.display-expression {
  font-size: 16px;
  color: #64748B;
  margin-bottom: 8px;
  text-align: right;
  min-height: 20px;
  word-break: break-all;
  line-height: 1.4;
}

.display-result {
  font-size: 36px;
  font-weight: bold;
  text-align: right;
  color: #1E293B;
  word-break: break-all;
  line-height: 1.2;
}

/* 模式切换 */
.mode-switch {
  margin-bottom: 20px;
  padding: 4px;
}

.switch-tabs {
  display: flex;
  background: #F1F5F9;
  border-radius: 8px;
  padding: 4px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  color: #64748B;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-item.active {
  background: #3B82F6;
  color: #FFFFFF;
}

/* 计算器网格 */
.calculator-grid {
  display: grid;
  gap: 10px;
  margin-bottom: 20px;
}

.basic-calc {
  grid-template-columns: repeat(4, 1fr);
}

.scientific-calc {
  grid-template-columns: repeat(5, 1fr);
}

/* 计算器按钮 */
.calc-btn {
  background: #FFFFFF;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  font-size: 20px;
  font-weight: 500;
  color: #1E293B;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 64px;
  padding: 8px 12px;
  position: relative;
  overflow: hidden;
}

.calc-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.calc-btn:active {
  transform: scale(0.95) translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.calc-btn:active::before {
  left: 100%;
}

.calc-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s ease-out, height 0.4s ease-out;
}

.calc-btn:active::after {
  width: 120px;
  height: 120px;
}

/* 按钮类型样式 */
.calc-btn.number {
  background: #FFFFFF;
  color: #1E293B;
}

.calc-btn.operator {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: #FFFFFF;
  font-weight: bold;
}

.calc-btn.function {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  color: #FFFFFF;
  font-size: 14px;
}

.calc-btn.equals {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: #FFFFFF;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
  position: relative;
  overflow: hidden;
}

.calc-btn.equals::before {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
}

.calc-btn.equals:active {
  transform: scale(0.92) translateY(2px);
  box-shadow: 0 2px 10px rgba(16, 185, 129, 0.5);
}

.calc-btn.clear {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: #FFFFFF;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.calc-btn.clear:active {
  transform: scale(0.90) translateY(2px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
}

/* 按钮宽度控制类 */
.btn-width-normal {
  width: 100%;
  min-width: 60px;
  max-width: 80px;
}

.btn-width-small {
  width: 80%;
  min-width: 50px;
  max-width: 65px;
}

.btn-width-large {
  width: 120%;
  min-width: 80px;
  max-width: 100px;
}

.btn-width-double {
  grid-column: span 2;
  width: 100%;
}

.btn-width-custom {
  width: var(--custom-width, 100%);
}

/* 特殊按钮 */
.calc-btn.zero {
  grid-column: span 2;
}

.calc-btn.zero-sci {
  grid-column: span 2;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  padding: 16px;
}

.action-btn {
  background: transparent;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.2s ease;
  height: 50px;
  width: 50px;
}

.action-btn:active {
  background: #F1F5F9;
}

.action-icon {
  font-size: 24px;
  margin-bottom: 4px;
  height: 50px;
  width: 50px;
  margin-top: -30px;
}

.action-text {
  font-size: 12px;
  color: #64748B;
  height: 50px;
  width: 50px;
}

/* 广告横幅 */
.ad-banner {
  text-align: center;
  margin-top: 20px;
}

.ad-text {
  font-size: 14px;
  color: #3B82F6;
}

/* 广告弹窗 */
.ad-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.ad-modal-content {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 24px;
  margin: 20px;
  max-width: 300px;
  width: 100%;
  text-align: center;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.ad-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.ad-header {
  margin-bottom: 24px;
}

.ad-title {
  font-size: 20px;
  font-weight: bold;
  color: #1E293B;
  margin-bottom: 8px;
  display: block;
}

.ad-subtitle {
  font-size: 14px;
  color: #64748B;
  line-height: 1.5;
}

.ad-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ad-buttons .btn-primary,
.ad-buttons .btn-secondary {
  width: 100%;
  margin: 0;
}

/* 历史记录弹窗 */
.history-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.history-modal-content {
  background: #FFFFFF;
  border-radius: 12px;
  margin: 20px;
  max-width: 90vw;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
  overflow: hidden;
}

.history-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #E2E8F0;
  margin-bottom: 0;
}

.history-title {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
}

.history-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.clear-history-btn {
  color: #EF4444;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.1);
}

.clear-history-btn:active {
  background: rgba(239, 68, 68, 0.2);
}

.close-btn {
  color: #64748B;
  font-size: 20px;
  padding: 4px;
  border-radius: 4px;
  background: #F1F5F9;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:active {
  background: #E2E8F0;
}

.history-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  min-height: 200px;
  max-height: calc(80vh - 120px);
}

.history-item {
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  transition: background 0.2s ease;
  overflow: hidden;
  width: 300px;
  height: 60px;
}

.history-item:active {
  background: #F1F5F9;
}

.history-expression {
  font-size: 14px;
  color: #64748B;
  margin-bottom: 6px;
  word-wrap: break-word;
  word-break: break-word;
  line-height: 1.4;
  max-width: 100%;
  overflow-wrap: break-word;
}

.history-result {
  font-size: 16px;
  font-weight: bold;
  color: #1E293B;
  margin-bottom: 6px;
  word-wrap: break-word;
  word-break: break-word;
  line-height: 1.3;
  max-width: 100%;
  overflow-wrap: break-word;
}

.history-time {
  font-size: 12px;
  color: #9CA3AF;
  text-align: right;
}

.empty-history {
  text-align: center;
  padding: 40px 20px;
  color: #9CA3AF;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 16px;
  }
  
  .display-result {
    font-size: 32px;
  }
  
  .calc-btn {
    min-height: 56px;
    font-size: 18px;
    padding: 6px 10px;
  }

  .calculator-grid {
    gap: 8px;
    padding: 0 6px;
  }
  
  .calc-btn.function {
    font-size: 12px;
  }

  .history-modal-content {
    max-width: 95vw;
    margin: 10px;
  }

  .history-list {
    padding: 12px;
    max-height: calc(80vh - 100px);
  }

  .history-item {
    padding: 10px;
  }

  .history-expression {
    font-size: 13px;
  }

  .history-result {
    font-size: 15px;
  }
  
  .scientific-calc {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .quick-actions {
    padding: 12px;
  }
  
  .action-icon {
    font-size: 20px;
  }
  
  .action-text {
    font-size: 11px;
  }
}

/* 滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 4px;
}

.history-list::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 2px;
}

.history-list::-webkit-scrollbar-thumb {
  background: #CBD5E1;
  border-radius: 2px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: #94A3B8;
}