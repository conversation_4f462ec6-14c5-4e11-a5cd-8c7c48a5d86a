App({
  globalData: {
    userInfo: null,
    adRevenue: {
      totalRevenue: 0,
      dailyRevenue: 0,
      adViews: 0,
      adClicks: 0
    },
    settings: {
      soundEnabled: true,
      vibrationEnabled: false,
      scientificMode: false,
      historyLimit: 100
    },
    calculatorHistory: []
  },

  onLaunch() {
    console.log('计算助手启动');
    this.loadUserData();
    this.initAd();
  },

  onShow() {
    console.log('计算助手显示');
  },

  onHide() {
    console.log('计算助手隐藏');
    this.saveUserData();
  },

  // 加载用户数据
  loadUserData() {
    try {
      const adRevenue = tt.getStorageSync('calc_ad_revenue');
      if (adRevenue) {
        this.globalData.adRevenue = JSON.parse(adRevenue);
      }

      const settings = tt.getStorageSync('calc_settings');
      if (settings) {
        this.globalData.settings = JSON.parse(settings);
      }

      const history = tt.getStorageSync('calc_history');
      if (history) {
        this.globalData.calculatorHistory = JSON.parse(history);
      }
    } catch (e) {
      console.error('加载用户数据失败:', e);
    }
  },

  // 保存用户数据
  saveUserData() {
    try {
      tt.setStorageSync('calc_ad_revenue', JSON.stringify(this.globalData.adRevenue));
      tt.setStorageSync('calc_settings', JSON.stringify(this.globalData.settings));
      tt.setStorageSync('calc_history', JSON.stringify(this.globalData.calculatorHistory));
    } catch (e) {
      console.error('保存用户数据失败:', e);
    }
  },

  // 初始化广告
  initAd() {
    // 创建激励视频广告实例
    if (tt.createRewardedVideoAd) {
      this.rewardedVideoAd = tt.createRewardedVideoAd({
        adUnitId: '4un6i9va4xgqy2lv24'
      });

      this.rewardedVideoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
      });

      this.rewardedVideoAd.onError((err) => {
        console.error('激励视频广告加载失败', err);
      });

      this.rewardedVideoAd.onClose((res) => {
        if (res && res.isEnded) {
          console.log('用户观看完整广告');
          this.recordAdRevenue('rewarded', 0.4);
        } else {
          console.log('用户提前关闭广告');
        }
      });
    }

    // 创建插屏广告实例
    if (tt.createInterstitialAd) {
      this.interstitialAd = tt.createInterstitialAd({
        adUnitId: 'your_calc_interstitial_ad_unit_id'
      });

      this.interstitialAd.onLoad(() => {
        console.log('插屏广告加载成功');
      });

      this.interstitialAd.onError((err) => {
        console.error('插屏广告加载失败', err);
      });

      this.interstitialAd.onClose(() => {
        console.log('插屏广告关闭');
        this.recordAdRevenue('interstitial', 0.25);
      });
    }
  },

  // 显示激励视频广告
  showRewardedVideoAd() {
    return new Promise((resolve, reject) => {
      if (this.rewardedVideoAd) {
        this.rewardedVideoAd.show().then(() => {
          resolve(true);
        }).catch((err) => {
          console.error('显示激励视频广告失败', err);
          reject(err);
        });
      } else {
        // 模拟广告播放
        setTimeout(() => {
          this.recordAdRevenue('rewarded', 0.4);
          resolve(true);
        }, 2000);
      }
    });
  },

  // 显示插屏广告
  showInterstitialAd() {
    if (this.interstitialAd) {
      this.interstitialAd.show().catch((err) => {
        console.error('显示插屏广告失败', err);
      });
    } else {
      // 模拟广告显示
      this.recordAdRevenue('interstitial', 0.25);
    }
  },

  // 记录广告收益
  recordAdRevenue(type, revenue) {
    this.globalData.adRevenue.adViews++;
    this.globalData.adRevenue.totalRevenue += revenue;
    this.globalData.adRevenue.dailyRevenue += revenue;
    this.saveUserData();
  },

  // 添加计算历史
  addCalculationHistory(expression, result) {
    const history = this.globalData.calculatorHistory;
    const record = {
      id: Date.now(),
      expression,
      result,
      timestamp: Date.now(),
      timeStr: this.formatTime(new Date())
    };

    history.unshift(record);
    
    // 限制历史记录数量
    const limit = this.globalData.settings.historyLimit;
    if (history.length > limit) {
      history.splice(limit);
    }

    this.globalData.calculatorHistory = history;
    this.saveUserData();
  },

  // 获取计算历史
  getCalculationHistory() {
    return this.globalData.calculatorHistory;
  },

  // 清空计算历史
  clearCalculationHistory() {
    this.globalData.calculatorHistory = [];
    this.saveUserData();
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return `${month}/${day} ${hour}:${minute.toString().padStart(2, '0')}`;
  },

  // 获取广告收益
  getAdRevenue() {
    return this.globalData.adRevenue;
  },

  // 获取设置
  getSettings() {
    return this.globalData.settings;
  },

  // 更新设置
  updateSettings(newSettings) {
    this.globalData.settings = { ...this.globalData.settings, ...newSettings };
    this.saveUserData();
  }
});