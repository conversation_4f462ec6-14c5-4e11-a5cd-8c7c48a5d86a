const app = getApp();

Page({
  data: {
    expression: '',
    result: '0',
    scientificMode: false,
    showAdModal: false,
    showHistoryModal: false,
    historyList: [],
    lastOperator: '',
    operandStack: [],
    operatorStack: [],
    isNewCalculation: true,
    hasDecimal: false
  },

  onLoad() {
    this.loadHistory();
    this.setData({
      scientificMode: app.getSettings().scientificMode
    });

    // 测试结果显示功能
    console.log('页面加载完成，当前result:', this.data.result);

    // 测试setData是否正常工作
    setTimeout(() => {
      console.log('测试setData功能...');
      this.setData({
        result: 'TEST'
      });
      console.log('测试完成，当前result:', this.data.result);

      // 恢复初始值
      setTimeout(() => {
        this.setData({
          result: '0'
        });
      }, 1000);
    }, 2000);
  },

  onShow() {
    this.loadHistory();
  },

  // 测试计算功能
  testCalculation() {
    console.log('=== 测试计算功能 ===');
    this.setData({
      expression: '2 + 3',
      result: '0'
    });

    setTimeout(() => {
      this.calculate();
    }, 100);
  },

  // 按钮点击处理
  onButtonTap(e) {
    const value = e.currentTarget.dataset.value;
    this.handleInput(value);
  },

  // 处理输入
  handleInput(value) {
    const { expression, result, isNewCalculation } = this.data;

    switch (value) {
      case 'C':
        this.clear();
        break;
      case '⌫':
        this.backspace();
        break;
      case '=':
        this.calculate();
        break;
      case '+':
      case '-':
      case '×':
      case '÷':
      case '%':
        this.handleOperator(value);
        break;
      case '.':
        this.handleDecimal();
        break;
      case '(':
      case ')':
        this.handleParenthesis(value);
        break;
      case 'sin':
      case 'cos':
      case 'tan':
      case 'ln':
      case 'log':
      case '√':
        this.handleFunction(value);
        break;
      case 'x²':
        this.handleSquare();
        break;
      case 'xʸ':
        this.handlePower();
        break;
      case 'π':
        this.handleConstant('π', Math.PI);
        break;
      case 'e':
        this.handleConstant('e', Math.E);
        break;
      case '!':
        this.handleFactorial();
        break;
      default:
        if (!isNaN(value)) {
          this.handleNumber(value);
        }
        break;
    }
  },

  // 处理数字输入
  handleNumber(num) {
    let { expression, result, isNewCalculation } = this.data;

    if (isNewCalculation) {
      // 如果是新计算，清空表达式，但保留当前结果显示
      expression = '';
      result = num; // 直接设置为新输入的数字
      this.setData({
        isNewCalculation: false,
        expression: num,
        result: num
      });
      return;
    }

    if (result === '0' && num !== '0') {
      result = num;
    } else if (result !== '0') {
      result += num;
    }

    expression += num;

    this.setData({
      expression,
      result
    });
  },

  // 处理运算符
  handleOperator(operator) {
    let { expression, result, isNewCalculation } = this.data;

    if (isNewCalculation) {
      expression = result;
      this.setData({ isNewCalculation: false });
    }

    // 如果表达式以运算符结尾，替换运算符
    if (/[+\-×÷%]$/.test(expression)) {
      expression = expression.slice(0, -1);
    }

    expression += ` ${operator} `;
    
    this.setData({
      expression,
      result: '0',
      hasDecimal: false
    });
  },

  // 处理小数点
  handleDecimal() {
    let { expression, result, hasDecimal } = this.data;

    if (hasDecimal) return;

    if (result === '0' || /[+\-×÷%]\s*$/.test(expression)) {
      result = '0.';
      expression += '0.';
    } else {
      result += '.';
      expression += '.';
    }

    this.setData({
      expression,
      result,
      hasDecimal: true
    });
  },

  // 处理括号
  handleParenthesis(paren) {
    let { expression } = this.data;
    
    expression += paren;
    
    this.setData({
      expression,
      result: paren
    });
  },

  // 处理函数
  handleFunction(func) {
    let { expression } = this.data;
    
    expression += `${func}(`;
    
    this.setData({
      expression,
      result: `${func}(`
    });
  },

  // 处理平方
  handleSquare() {
    let { expression, result } = this.data;
    
    if (result && !isNaN(result)) {
      const num = parseFloat(result);
      const squared = Math.pow(num, 2);
      
      expression = expression.replace(new RegExp(result + '$'), `(${result})²`);
      
      this.setData({
        expression,
        result: squared.toString()
      });
    }
  },

  // 处理幂运算
  handlePower() {
    this.handleOperator('^');
  },

  // 处理常数
  handleConstant(symbol, value) {
    let { expression, result, isNewCalculation } = this.data;

    if (isNewCalculation) {
      expression = '';
      this.setData({ isNewCalculation: false });
    }

    expression += symbol;
    result = value.toString();

    this.setData({
      expression,
      result
    });
  },

  // 处理阶乘
  handleFactorial() {
    let { expression, result } = this.data;
    
    if (result && !isNaN(result)) {
      const num = parseInt(result);
      if (num >= 0 && num <= 20) {
        const factorial = this.calculateFactorial(num);
        
        expression = expression.replace(new RegExp(result + '$'), `${result}!`);
        
        this.setData({
          expression,
          result: factorial.toString()
        });
      } else {
        tt.showToast({
          title: '数值过大或无效',
          icon: 'none'
        });
      }
    }
  },

  // 计算阶乘
  calculateFactorial(n) {
    if (n <= 1) return 1;
    return n * this.calculateFactorial(n - 1);
  },

  // 退格
  backspace() {
    let { expression, result } = this.data;

    if (result.length > 1) {
      result = result.slice(0, -1);
    } else {
      result = '0';
    }

    if (expression.length > 0) {
      expression = expression.slice(0, -1);
    }

    this.setData({
      expression,
      result
    });
  },

  // 清除
  clear() {
    this.setData({
      expression: '',
      result: '0',
      isNewCalculation: true,
      hasDecimal: false
    });
  },

  // 计算结果
  calculate() {
    const { expression } = this.data;

    console.log('=== 开始计算 ===');
    console.log('当前表达式:', expression);
    console.log('当前result:', this.data.result);

    if (!expression || expression.trim() === '') {
      console.log('表达式为空，无法计算');
      tt.showToast({
        title: '请输入计算表达式',
        icon: 'none'
      });
      return;
    }

    try {
      // 替换显示符号为计算符号
      let calcExpression = expression
        .trim()
        .replace(/×/g, '*')
        .replace(/÷/g, '/')
        .replace(/π/g, Math.PI.toString())
        .replace(/e(?![0-9])/g, Math.E.toString())
        .replace(/sin\(/g, 'Math.sin(')
        .replace(/cos\(/g, 'Math.cos(')
        .replace(/tan\(/g, 'Math.tan(')
        .replace(/ln\(/g, 'Math.log(')
        .replace(/log\(/g, 'Math.log10(')
        .replace(/√\(/g, 'Math.sqrt(')
        .replace(/\^/g, '**');

      console.log('转换后的表达式:', calcExpression);

      // 处理阶乘
      calcExpression = calcExpression.replace(/(\d+)!/g, (match, num) => {
        return this.calculateFactorial(parseInt(num));
      });

      // 处理平方
      calcExpression = calcExpression.replace(/\(([^)]+)\)²/g, 'Math.pow($1, 2)');

      // 验证表达式安全性（放宽验证规则）
      const cleanExpression = calcExpression.replace(/Math\.(sin|cos|tan|log|log10|sqrt|pow)/g, '').replace(/[0-9.]+/g, '');
      if (/[a-zA-Z_$]/.test(cleanExpression.replace(/Math|PI|E/g, ''))) {
        console.log('表达式包含不安全字符:', cleanExpression);
        throw new Error('表达式格式错误');
      }

      console.log('最终计算表达式:', calcExpression);
      const result = eval(calcExpression);
      console.log('计算结果:', result);

      if (isNaN(result) || !isFinite(result)) {
        throw new Error('计算结果无效');
      }

      const formattedResult = this.formatResult(result);
      console.log('格式化后的结果:', formattedResult);

      // === 关键：立即更新界面显示结果 ===
      console.log('=== 更新界面显示 ===');
      console.log('更新前 - expression:', this.data.expression);
      console.log('更新前 - result:', this.data.result);

      // 使用同步方式更新数据，确保结果立即显示
      this.setData({
        result: formattedResult,
        isNewCalculation: true,
        hasDecimal: formattedResult.includes('.')
      });

      console.log('更新后 - result:', this.data.result);
      console.log('=== 界面更新完成 ===');

      // 保存到历史记录
      try {
        app.addCalculationHistory(expression, formattedResult);
        console.log('历史记录已保存');
      } catch (historyError) {
        console.error('保存历史记录失败:', historyError);
      }

      console.log('=== 计算完成，结果已显示 ===');

      // 延迟显示广告，确保不影响结果显示
      setTimeout(() => {
        console.log('准备显示广告，当前result仍为:', this.data.result);
        this.showCalculationAd();
      }, 1000);

    } catch (error) {
      console.error('=== 计算错误 ===', error);
      tt.showToast({
        title: '计算错误',
        icon: 'none'
      });

      // 显示错误信息
      this.setData({
        result: 'Error',
        isNewCalculation: true
      });
      console.log('错误信息已显示在界面');
    }
  },

  // 格式化结果
  formatResult(result) {
    if (Number.isInteger(result)) {
      return result.toString();
    } else {
      // 保留最多10位小数，去除末尾的0
      return parseFloat(result.toFixed(10)).toString();
    }
  },

  // 切换模式
  switchMode(e) {
    const mode = e.currentTarget.dataset.mode;
    const scientificMode = mode === 'scientific';

    // 直接切换模式，无需任何限制
    this.setData({
      scientificMode
    });

    // 保存设置
    app.updateSettings({ scientificMode });
  },

  // 显示计算完成后的广告
  async showCalculationAd() {
    try {
      console.log('显示计算完成广告');
      // 使用视频广告ID
      await app.showRewardedVideoAd();
      console.log('广告显示成功');
    } catch (error) {
      console.log('广告显示失败，但不影响计算结果和历史记录:', error);
      // 广告失败不影响计算功能和历史记录保存
    }
  },

  // 观看广告
  async watchAd() {
    try {
      tt.showLoading({
        title: '加载广告中...'
      });

      await app.showRewardedVideoAd();
      
      tt.hideLoading();
      tt.showToast({
        title: '解锁成功！',
        icon: 'success'
      });

      // 解锁科学计算器
      app.updateSettings({ scientificMode: true });

      this.setData({
        showAdModal: false,
        scientificMode: true
      });

    } catch (error) {
      tt.hideLoading();
      tt.showToast({
        title: '广告加载失败',
        icon: 'none'
      });
    }
  },

  // 关闭广告弹窗
  closeAdModal() {
    this.setData({
      showAdModal: false
    });
  },

  // 显示历史记录
  showHistory() {
    this.loadHistory();
    this.setData({
      showHistoryModal: true
    });
  },

  // 关闭历史记录
  closeHistory() {
    this.setData({
      showHistoryModal: false
    });
  },

  // 加载历史记录
  loadHistory() {
    const history = app.getCalculationHistory();
    this.setData({
      historyList: history.slice(0, 20) // 只显示最近20条
    });
  },

  // 使用历史记录项
  useHistoryItem(e) {
    const item = e.currentTarget.dataset.item;
    
    this.setData({
      expression: item.expression,
      result: item.result,
      showHistoryModal: false,
      isNewCalculation: false
    });
  },

  // 清空历史记录
  clearHistory() {
    tt.showModal({
      title: '确认清空',
      content: '确定要清空所有计算历史吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearCalculationHistory();
          this.setData({
            historyList: []
          });
          tt.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 复制结果
  copyResult() {
    const { result } = this.data;
    
    if (result && result !== '0' && result !== 'Error') {
      tt.setClipboardData({
        data: result,
        success: () => {
          tt.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          });
        }
      });
    }
  },

  // 清空所有
  clearAll() {
    this.clear();
    tt.showToast({
      title: '已清空',
      icon: 'success'
    });
  }
});