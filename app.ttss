/* 全局样式 */
page {
  background-color: #F8FAFC;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #1E293B;
}

/* 通用容器 */
.container {
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 16px;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  color: #FFFFFF;
  border: none;
  border-radius: 25px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: #F1F5F9;
  color: #64748B;
  border: 1px solid #E2E8F0;
  border-radius: 25px;
  padding: 12px 30px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.btn-secondary:active {
  background: #E2E8F0;
}

/* 计算器按钮样式 */
.calc-btn {
  background: #FFFFFF;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 500;
  color: #1E293B;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.calc-btn:active {
  background: #F8FAFC;
  transform: scale(0.98);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.calc-btn.number {
  background: #FFFFFF;
  color: #1E293B;
}

.calc-btn.operator {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: #FFFFFF;
  font-weight: bold;
}

.calc-btn.function {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  color: #FFFFFF;
  font-size: 14px;
}

.calc-btn.equals {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: #FFFFFF;
  font-weight: bold;
}

.calc-btn.clear {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: #FFFFFF;
  font-weight: bold;
}

/* 输入框样式 */
.input {
  background: #F8FAFC;
  border: 2px solid #E2E8F0;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  color: #1E293B;
  transition: border-color 0.3s ease;
}

.input:focus {
  border-color: #3B82F6;
  outline: none;
}

/* 文本样式 */
.text-primary {
  color: #1E293B;
}

.text-secondary {
  color: #64748B;
}

.text-success {
  color: #10B981;
}

.text-warning {
  color: #F59E0B;
}

.text-error {
  color: #EF4444;
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 标题样式 */
.title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  opacity: 0.8;
  margin-bottom: 20px;
}

/* 显示屏样式 */
.display {
  background: #1E293B;
  color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.display-expression {
  font-size: 16px;
  color: #94A3B8;
  margin-bottom: 8px;
  text-align: right;
  min-height: 20px;
}

.display-result {
  font-size: 32px;
  font-weight: bold;
  text-align: right;
  color: #FFFFFF;
  word-break: break-all;
}

/* 列表样式 */
.list-item {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  border-left: 4px solid #3B82F6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 分割线 */
.divider {
  height: 1px;
  background: #E2E8F0;
  margin: 20px 0;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #E2E8F0;
  border-top: 2px solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 广告样式 */
.ad-banner {
  background: linear-gradient(135deg, #EBF4FF 0%, #DBEAFE 100%);
  border: 1px solid #BFDBFE;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  margin: 16px 0;
}

.ad-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.ad-modal-content {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 24px;
  margin: 20px;
  max-width: 300px;
  width: 100%;
  text-align: center;
  animation: modalShow 0.3s ease-out;
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #CBD5E1;
  transition: 0.3s;
  border-radius: 28px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #3B82F6;
}

input:checked + .slider:before {
  transform: translateX(22px);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 16px;
  }
  
  .card {
    padding: 16px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .display-result {
    font-size: 28px;
  }
  
  .calc-btn {
    min-height: 50px;
    font-size: 16px;
  }
}