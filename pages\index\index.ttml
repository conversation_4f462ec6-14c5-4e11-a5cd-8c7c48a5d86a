<view class="container">
  <!-- 计算器显示屏 -->
  <view class="display card">
    <view class="display-expression">{{expression}}</view>
    <view class="display-result">{{result}}</view>
  </view>

  <!-- 功能切换 -->
  <view class="mode-switch card">
    <view class="switch-tabs">
      <view class="tab-item {{!scientificMode ? 'active' : ''}}" bindtap="switchMode" data-mode="basic">
        <text>基础</text>
      </view>
      <view class="tab-item {{scientificMode ? 'active' : ''}}" bindtap="switchMode" data-mode="scientific">
        <text>科学</text>
      </view>
    </view>
  </view>

  <!-- 基础计算器 -->
  <view class="calculator-grid basic-calc" tt:if="{{!scientificMode}}">
    <!-- 第一行：清除、退格、百分号、除法 -->
    <button class="calc-btn clear btn-width-normal" bindtap="onButtonTap" data-value="C">C</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="⌫">⌫</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="%">%</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="÷">÷</button>

    <!-- 第二行：7、8、9、乘法 -->
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="7">7</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="8">8</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="9">9</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="×">×</button>

    <!-- 第三行：4、5、6、减法 -->
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="4">4</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="5">5</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="6">6</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="-">-</button>

    <!-- 第四行：1、2、3、加法 -->
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="1">1</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="2">2</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="3">3</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="+">+</button>

    <!-- 第五行：0、小数点、等号 -->
    <button class="calc-btn number zero btn-width-double" bindtap="onButtonTap" data-value="0">0</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value=".">.</button>
    <button class="calc-btn equals btn-width-normal" bindtap="onButtonTap" data-value="=">=</button>
  </view>

  <!-- 科学计算器 -->
  <view class="calculator-grid scientific-calc" tt:if="{{scientificMode}}">
    <!-- 第一行：函数按钮 -->
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="sin">sin</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="cos">cos</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="tan">tan</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="ln">ln</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="log">log</button>

    <!-- 第二行：更多函数 -->
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="√">√</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="x²">x²</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="xʸ">xʸ</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="π">π</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="e">e</button>

    <!-- 第三行：清除、退格、括号 -->
    <button class="calc-btn clear btn-width-normal" bindtap="onButtonTap" data-value="C">C</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="⌫">⌫</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="(">(</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value=")">)</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="÷">÷</button>

    <!-- 第四行：7、8、9、乘法 -->
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="7">7</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="8">8</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="9">9</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="×">×</button>
    <button class="calc-btn function btn-width-normal" bindtap="onButtonTap" data-value="!">!</button>

    <!-- 第五行：4、5、6、减法 -->
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="4">4</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="5">5</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="6">6</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="-">-</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="%">%</button>

    <!-- 第六行：1、2、3、加法 -->
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="1">1</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="2">2</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value="3">3</button>
    <button class="calc-btn operator btn-width-normal" bindtap="onButtonTap" data-value="+">+</button>
    <button class="calc-btn function btn-width-normal" bindtap="showHistory">历史</button>

    <!-- 第七行：0、小数点、等号 -->
    <button class="calc-btn number zero-sci btn-width-double" bindtap="onButtonTap" data-value="0">0</button>
    <button class="calc-btn number btn-width-normal" bindtap="onButtonTap" data-value=".">.</button>
    <button class="calc-btn equals zero-sci btn-width-double" bindtap="onButtonTap" data-value="=">=</button>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions card">
    <button class="action-btn" bindtap="showHistory">
      <text class="action-icon">📋</text>
      <text class="action-text">历史记录</text>
    </button>
    <button class="action-btn" bindtap="copyResult">
      <text class="action-icon">📋</text>
      <text class="action-text">复制结果</text>
    </button>
  </view>

  <!-- 广告横幅 -->
  <view class="ad-banner">
    <text class="ad-text">💡 更多计算工具推荐</text>
  </view>
</view>

<!-- 广告解锁弹窗 -->
<view class="ad-modal" tt:if="{{showAdModal}}">
  <view class="ad-modal-content">
    <view class="ad-header">
      <text class="ad-title">🎁 解锁科学计算器</text>
      <text class="ad-subtitle">观看广告即可免费使用科学计算功能</text>
    </view>
    <view class="ad-buttons">
      <button class="btn-primary" bindtap="watchAd">观看广告解锁</button>
      <button class="btn-secondary" bindtap="closeAdModal">稍后再说</button>
    </view>
  </view>
  <view class="ad-modal-mask" bindtap="closeAdModal"></view>
</view>

<!-- 历史记录弹窗 -->
<view class="history-modal" tt:if="{{showHistoryModal}}">
  <view class="history-modal-content card">
    <view class="history-header">
      <text class="history-title">计算历史</text>
      <view class="history-actions">
        <text class="clear-history-btn" bindtap="clearHistory">清空</text>
        <text class="close-btn" bindtap="closeHistory">✕</text>
      </view>
    </view>
    <scroll-view class="history-list" scroll-y>
      <view class="history-item" tt:for="{{historyList}}" tt:key="id" bindtap="useHistoryItem" data-item="{{item}}">
        <view class="history-expression">{{item.expression}}</view>
        <view class="history-result">= {{item.result}}</view>
        <view class="history-time">{{item.timeStr}}</view>
      </view>
      <view class="empty-history" tt:if="{{historyList.length === 0}}">
        <text>暂无计算记录</text>
      </view>
    </scroll-view>
  </view>
  <view class="history-modal-mask" bindtap="closeHistory"></view>
</view>